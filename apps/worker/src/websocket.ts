import { Server } from 'http';
import { WebSocket, WebSocketServer } from 'ws';

interface BuildStatus {
  jobId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  message: string;
  progress?: number;
  error?: string;
}

class WebSocketManager {
  private wss: WebSocketServer;
  private clients: Map<string, WebSocket> = new Map();

  constructor(server: Server) {
    this.wss = new WebSocketServer({ server });
    this.setupWebSocketServer();
  }

  private setupWebSocketServer() {
    this.wss.on('connection', (ws: WebSocket) => {
      const clientId = Math.random().toString(36).substring(7);
      this.clients.set(clientId, ws);

      ws.on('close', () => {
        this.clients.delete(clientId);
      });

      // Send initial connection success message
      this.sendToClient(clientId, {
        type: 'connected',
        message: 'Connected to build status server',
      });
    });
  }

  public broadcastBuildStatus(status: BuildStatus) {
    const message = JSON.stringify({
      type: 'build-status',
      data: status,
    });

    this.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  private sendToClient(clientId: string, message: any) {
    const client = this.clients.get(clientId);
    if (client && client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify(message));
    }
  }
}

export { BuildStatus, WebSocketManager };
