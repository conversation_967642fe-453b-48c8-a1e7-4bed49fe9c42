import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, Loader2, XCircle } from 'lucide-react';
import { useEffect, useState } from 'react';

export type BuildStatus = 'processing' | 'completed' | 'failed';

export type BuildProgressMessage =
  | {
      type: 'build-status';
      data: {
        jobId: string;
        status: BuildStatus;
        message: string;
        progress: number;
        error?: string;
      };
    }
  | {
      type: 'connected';
      message: string;
    };

interface BuildProgressProps {
  messages: BuildProgressMessage[];
}

export const BuildProgress = ({ messages }: BuildProgressProps) => {
  const [currentProgress, setCurrentProgress] = useState(0);
  const [currentStatus, setCurrentStatus] = useState<BuildStatus>('processing');
  const [currentMessage, setCurrentMessage] = useState('Initializing build...');
  const [error, setError] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (messages.length === 0) return;

    // Get the latest message
    const latestMessage = messages[messages.length - 1];

    if (latestMessage.type === 'build-status') {
      setCurrentProgress(latestMessage.data.progress || 0);
      setCurrentStatus(latestMessage.data.status);
      setCurrentMessage(latestMessage.data.message);

      if (latestMessage.data.error) {
        setError(latestMessage.data.error);
      }
    }
  }, [messages]);

  return (
    <div className="w-full h-full flex flex-col items-center justify-center p-6">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {currentStatus === 'processing' && (
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
            )}
            {currentStatus === 'completed' && (
              <CheckCircle className="h-5 w-5 text-green-500" />
            )}
            {currentStatus === 'failed' && (
              <XCircle className="h-5 w-5 text-red-500" />
            )}
            Build{' '}
            {currentStatus === 'processing' ? 'in progress' : currentStatus}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{currentMessage}</span>
              <span>{currentProgress}%</span>
            </div>
            <Progress
              value={currentProgress}
              max={100}
              className="h-2"
              indicatorClassName={
                currentStatus === 'completed'
                  ? 'bg-green-500'
                  : currentStatus === 'failed'
                    ? 'bg-red-500'
                    : undefined
              }
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertTitle>Build Failed</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
